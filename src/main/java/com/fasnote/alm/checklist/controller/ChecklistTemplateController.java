package com.fasnote.alm.checklist.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fasnote.alm.checklist.dto.ApiResponse;
import com.fasnote.alm.checklist.dto.ResponseBuilder;
import com.fasnote.alm.checklist.model.ChecklistTemplate;
import com.fasnote.alm.checklist.service.ChecklistTemplateService;

/**
 * 检查单模板管理控制器
 * 提供模板管理的 REST API 接口
 */
@RestController
@RequestMapping("/templates")
public class ChecklistTemplateController {

    private final ChecklistTemplateService templateService;

    public ChecklistTemplateController(ChecklistTemplateService templateService) {
        this.templateService = templateService;
    }
    
    /**
     * 获取所有模板
     * GET /api/templates?projectId=xxx
     * GET /api/templates?projectId=xxx&type=xxx (支持按类型过滤)
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<ChecklistTemplate>>> getAllTemplates(
            @RequestParam String projectId,
            @RequestParam(required = false) String type) {
        try {
            // 验证项目ID
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            List<ChecklistTemplate> templates;
            if (type != null && !type.trim().isEmpty()) {
                // 按类型过滤
                templates = templateService.getTemplatesByType(projectId.trim(), type.trim());
            } else {
                // 获取所有模板
                templates = templateService.getAllTemplates(projectId.trim());
            }
            return ResponseBuilder.success(templates);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("TEMPLATE_READ_ERROR", "读取模板列表失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取模板列表时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 获取所有模板类型
     * GET /api/templates/types?projectId=xxx
     */
    @GetMapping("/types")
    public ResponseEntity<ApiResponse<List<String>>> getTemplateTypes(@RequestParam String projectId) {
        try {
            // 验证项目ID
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }

            List<String> types = templateService.getTemplateTypes(projectId.trim());
            return ResponseBuilder.success(types);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("TEMPLATE_READ_ERROR", "读取模板类型失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取模板类型时发生未知错误: " + e.getMessage());
        }
    }

    /**
     * 根据类型获取模板
     * GET /api/templates/{type}?projectId=xxx
     */
    @GetMapping("/{type}")
    public ResponseEntity<ApiResponse<List<ChecklistTemplate>>> getTemplatesByType(
            @PathVariable String type,
            @RequestParam String projectId) {
        try {
            // 参数验证
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }
            if (type == null || type.trim().isEmpty()) {
                return ResponseBuilder.badRequest("模板类型不能为空");
            }

            List<ChecklistTemplate> templates = templateService.getTemplatesByType(projectId.trim(), type.trim());
            return ResponseBuilder.success(templates);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("TEMPLATE_READ_ERROR", "读取模板失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取模板时发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 创建新模板
     * POST /api/templates?projectId=xxx
     */
    @PostMapping
    public ResponseEntity<ApiResponse<ChecklistTemplate>> createTemplate(
            @RequestParam String projectId,
            @RequestBody ChecklistTemplate template) {
        try {
            // 基本参数验证
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }
            if (template == null) {
                return ResponseBuilder.badRequest("请求体不能为空");
            }

            ChecklistTemplate createdTemplate = templateService.createTemplate(projectId.trim(), template);
            return ResponseBuilder.created(createdTemplate);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("TEMPLATE_SAVE_ERROR", "保存模板失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "创建模板时发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 更新模板
     * PUT /api/templates/{id}?projectId=xxx
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<ChecklistTemplate>> updateTemplate(
            @PathVariable String id,
            @RequestParam String projectId,
            @RequestBody ChecklistTemplate template) {
        try {
            // 基本参数验证
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("模板ID不能为空");
            }
            if (template == null) {
                return ResponseBuilder.badRequest("请求体不能为空");
            }

            ChecklistTemplate updatedTemplate = templateService.updateTemplate(projectId.trim(), id.trim(), template);
            return ResponseBuilder.success(updatedTemplate);
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("TEMPLATE_UPDATE_ERROR", "更新模板失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "更新模板时发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 删除模板
     * DELETE /api/templates/{id}?projectId=xxx
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteTemplate(
            @PathVariable String id,
            @RequestParam String projectId) {
        try {
            // 参数验证
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("模板ID不能为空");
            }

            templateService.deleteTemplate(projectId.trim(), id.trim());
            return ResponseBuilder.success("模板删除成功");
        } catch (IllegalArgumentException e) {
            return ResponseBuilder.validationError(e.getMessage());
        } catch (IOException e) {
            return ResponseBuilder.error("TEMPLATE_DELETE_ERROR", "删除模板失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "删除模板时发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取单个模板
     * GET /api/templates/detail/{id}?projectId=xxx&includeConfig=true 返回完整配置
     * GET /api/templates/detail/{id}?projectId=xxx 返回基本信息
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<ApiResponse<Object>> getTemplateById(
            @PathVariable String id,
            @RequestParam String projectId,
            @RequestParam(value = "includeConfig", defaultValue = "false") boolean includeConfig) {
        try {
            // 参数验证
            if (projectId == null || projectId.trim().isEmpty()) {
                return ResponseBuilder.badRequest("项目ID不能为空");
            }
            if (id == null || id.trim().isEmpty()) {
                return ResponseBuilder.badRequest("模板ID不能为空");
            }

            Optional<ChecklistTemplate> templateOpt = templateService.getTemplateById(projectId.trim(), id.trim());
            if (templateOpt.isPresent()) {
                ChecklistTemplate template = templateOpt.get();

                if (includeConfig) {
                    // 返回完整配置格式（兼容前端的 template-config 接口期望）
                    Map<String, Object> configResponse = new HashMap<>();
                    configResponse.put("template", template);
                    configResponse.put("tableConfig", template.getTableConfig());
                    configResponse.put("defectRules", template.getDefectRules() != null ? template.getDefectRules() : new Object[0]);
                    configResponse.put("statusButtons", template.getStatusButtons() != null ? template.getStatusButtons() : new Object[0]);
                    configResponse.put("completionButtons", template.getCompletionButtons());

                    return ResponseBuilder.success(configResponse);
                } else {
                    // 返回基本模板信息
                    return ResponseBuilder.success(template);
                }
            } else {
                return ResponseBuilder.notFound("模板不存在: " + id);
            }
        } catch (IOException e) {
            return ResponseBuilder.error("TEMPLATE_READ_ERROR", "读取模板失败: " + e.getMessage());
        } catch (Exception e) {
            return ResponseBuilder.error("UNKNOWN_ERROR", "获取模板时发生未知错误: " + e.getMessage());
        }
    }
}